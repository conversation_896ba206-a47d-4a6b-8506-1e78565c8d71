#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import glob
import re
from datetime import datetime

def merge_keys():
    """合并所有找到的密钥到一个文件"""
    
    data_path = "data"
    keys_path = os.path.join(data_path, "keys")
    
    if not os.path.exists(keys_path):
        print("❌ 密钥目录不存在: data/keys/")
        return
    
    # 查找所有密钥文件
    key_files = []
    patterns = [
        "keys_valid_*.txt",
        "keys_found_*.txt", 
        "key_*.txt"
    ]
    
    for pattern in patterns:
        files = glob.glob(os.path.join(keys_path, pattern))
        key_files.extend(files)
    
    if not key_files:
        print("❌ 未找到任何密钥文件")
        return
    
    print(f"📁 找到 {len(key_files)} 个密钥文件:")
    for file in key_files:
        print(f"   - {os.path.basename(file)}")
    
    # 收集所有密钥
    all_keys = set()  # 使用set去重
    
    for file_path in key_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # 验证是否是有效的API密钥格式
                        if is_valid_api_key(line):
                            all_keys.add(line)
        except Exception as e:
            print(f"⚠️ 读取文件失败 {file_path}: {e}")
    
    if not all_keys:
        print("❌ 未找到有效的密钥")
        return
    
    # 生成合并文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    merged_file = os.path.join(keys_path, f"all_keys_merged_{timestamp}.txt")
    
    # 按类型分类密钥
    gemini_keys = []
    other_keys = []
    
    for key in sorted(all_keys):
        if key.startswith('AIzaSy'):
            gemini_keys.append(key)
        else:
            other_keys.append(key)
    
    # 写入合并文件
    with open(merged_file, 'w', encoding='utf-8') as f:
        f.write(f"# Hajimi King 密钥合并文件\n")
        f.write(f"# 生成时间: {datetime.now()}\n")
        f.write(f"# 总计密钥数量: {len(all_keys)}\n")
        f.write(f"# Gemini密钥: {len(gemini_keys)}\n")
        f.write(f"# 其他密钥: {len(other_keys)}\n")
        f.write("#" + "="*50 + "\n\n")
        
        if gemini_keys:
            f.write("# Gemini API 密钥\n")
            for key in gemini_keys:
                f.write(f"{key}\n")
            f.write("\n")
        
        if other_keys:
            f.write("# 其他 API 密钥\n")
            for key in other_keys:
                f.write(f"{key}\n")
    
    print(f"\n✅ 密钥合并完成!")
    print(f"📄 合并文件: {merged_file}")
    print(f"🔑 总计密钥数量: {len(all_keys)}")
    print(f"   - Gemini密钥: {len(gemini_keys)}")
    print(f"   - 其他密钥: {len(other_keys)}")
    
    # 创建一个纯净版本（只有密钥，无注释）
    clean_file = os.path.join(keys_path, f"all_keys_clean_{timestamp}.txt")
    with open(clean_file, 'w', encoding='utf-8') as f:
        for key in sorted(all_keys):
            f.write(f"{key}\n")
    
    print(f"📄 纯净版本: {clean_file}")
    
    return merged_file, len(all_keys)

def is_valid_api_key(key):
    """验证是否是有效的API密钥格式"""
    patterns = [
        r'^AIzaSy[A-Za-z0-9_-]{33,}$',  # Gemini API Key
        r'^sk-[A-Za-z0-9]{48,}$',       # OpenAI API Key
        r'^[A-Za-z0-9_-]{32,}$',        # 通用API Key
    ]
    
    for pattern in patterns:
        if re.match(pattern, key):
            return True
    
    return False

def show_key_statistics():
    """显示密钥统计信息"""
    keys_path = os.path.join("data", "keys")
    
    if not os.path.exists(keys_path):
        print("❌ 密钥目录不存在")
        return
    
    # 统计各类文件
    all_files = glob.glob(os.path.join(keys_path, "*.txt"))
    
    print("\n📊 密钥文件统计:")
    print(f"   总文件数: {len(all_files)}")
    
    for file_path in all_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            print(f"   {os.path.basename(file_path)}: {len(lines)} 行")
        except:
            print(f"   {os.path.basename(file_path)}: 读取失败")

if __name__ == "__main__":
    print("🔑 Hajimi King 密钥合并工具")
    print("="*50)
    
    # 显示统计信息
    show_key_statistics()
    
    print("\n开始合并密钥...")
    try:
        result = merge_keys()
        if result:
            print(f"\n🎉 合并成功完成!")
    except Exception as e:
        print(f"❌ 合并失败: {e}")
    
    input("\n按回车键退出...")
